"""API请求模型

定义所有API端点的请求数据模型。
"""

from typing import Optional
from pydantic import BaseModel, Field


class BuildRequest(BaseModel):
    """构建请求模型"""
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")


class DeployRequest(BaseModel):
    """部署请求模型"""
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")


class BuildPluginRequest(BaseModel):
    """插件编译请求模型"""
    plugin_name: str = Field("postgre_parser.so", description="插件名称")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")

class DeployPluginRequest(BaseModel):
    """插件部署请求模型"""
    plugin_name: str = Field("postgre_parser.so", description="插件名称")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")


class BuildDeployRequest(BaseModel):
    """构建部署统一请求模型

    注意：SSH用户名和密码将自动从凭据存储中获取，基于提供的服务器IP。
    如果凭据存储中没有对应的凭据，将使用配置文件中的默认值。
    """
    # 编译服务器配置
    build_remote_ip: Optional[str] = Field("", description="编译远程服务器IP")
    build_ssh_user: Optional[str] = Field("", description="编译服务器SSH用户名（可选，优先从凭据存储获取）")
    container_name: Optional[str] = Field("", description="容器名称")

    # 部署服务器配置
    deploy_remote_ip: Optional[str] = Field("", description="部署远程服务器IP")
    deploy_ssh_user: Optional[str] = Field("", description="部署服务器SSH用户名（可选，优先从凭据存储获取）")


class BuildDeployPluginRequest(BaseModel):
    """插件构建部署统一请求模型

    注意：SSH用户名和密码将自动从凭据存储中获取，基于提供的服务器IP。
    如果凭据存储中没有对应的凭据，将使用配置文件中的默认值。
    """
    # 插件信息
    plugin_name: str = Field("postgre_parser.so", description="插件名称")

    # 编译服务器配置
    build_remote_ip: Optional[str] = Field("", description="编译远程服务器IP")
    build_ssh_user: Optional[str] = Field("", description="编译服务器SSH用户名（可选，优先从凭据存储获取）")
    container_name: Optional[str] = Field("", description="容器名称")

    # 部署服务器配置
    deploy_remote_ip: Optional[str] = Field("", description="部署远程服务器IP")
    deploy_ssh_user: Optional[str] = Field("", description="部署服务器SSH用户名（可选，优先从凭据存储获取）")


class PcapTestRequest(BaseModel):
    """PCAP测试请求模型"""
    pcap_file_path: str = Field(..., description="pcap文件路径")
    protocol_name: str = Field(..., description="测试协议名称，如 'postgre', 'mysql', 'redis' 等")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")


class PcapUploadRequest(BaseModel):
    """PCAP上传测试请求模型（用于表单数据验证）"""
    protocol_name: str = Field(..., description="测试协议名称，如 'postgre', 'mysql', 'redis' 等")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")


class PcapReplayRequest(BaseModel):
    """PCAP回放测试请求模型（用于表单数据验证）"""
    protocol_name: str = Field(..., description="测试协议名称，如 'postgre', 'mysql', 'redis' 等")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    eth_name: Optional[str] = Field("lo", description="网口名称")