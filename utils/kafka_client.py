"""Kafka客户端工具

提供Kafka消费者功能，用于获取JSON事件数据。
"""

import json
import asyncio
from typing import List, Dict, Any, Optional
from aiokafka import AIOKafkaConsumer
from aiokafka.errors import KafkaError

from config.settings import settings
from core.logging import get_logger

logger = get_logger(__name__)


class KafkaClient:
    """Kafka客户端"""

    def __init__(self):
        self.consumer: Optional[AIOKafkaConsumer] = None
        self.is_connected = False

    async def connect(self) -> bool:
        """连接到Kafka服务器
        
        Returns:
            是否连接成功
        """
        try:
            self.consumer = AIOKafkaConsumer(
                settings.kafka_topic_db_event,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=settings.kafka_consumer_group,
                auto_offset_reset='latest',  # 从最新消息开始消费
                enable_auto_commit=True,
                value_deserializer=lambda m: m.decode('utf-8') if m else None
            )
            
            await self.consumer.start()
            self.is_connected = True
            logger.info(f"Kafka消费者连接成功: {settings.kafka_bootstrap_servers}")
            return True
            
        except Exception as e:
            logger.error(f"Kafka消费者连接失败: {e}")
            # 如果是压缩库缺失错误，提供详细的解决方案
            if "snappy" in str(e).lower() or "UnsupportedCodecError" in str(e):
                logger.error("Kafka snappy压缩库缺失，请安装: pip install python-snappy")
                logger.error("如果仍有问题，可能需要安装系统级snappy库:")
                logger.error("  Ubuntu/Debian: sudo apt-get install libsnappy-dev")
                logger.error("  CentOS/RHEL: sudo yum install snappy-devel")
                logger.error("  macOS: brew install snappy")
            self.is_connected = False
            return False

    async def disconnect(self):
        """断开Kafka连接"""
        if self.consumer:
            try:
                await self.consumer.stop()
                self.consumer = None
                self.is_connected = False
                logger.info("Kafka消费者连接已断开")
            except Exception as e:
                logger.error(f"断开Kafka连接失败: {e}")
                # 即使断开失败，也要重置状态
                self.consumer = None
                self.is_connected = False

    async def consume_events(self, timeout_seconds: int = 10, max_records: int = 100) -> List[Dict[str, Any]]:
        """消费JSON事件数据
        
        Args:
            timeout_seconds: 消费超时时间（秒）
            max_records: 最大记录数
            
        Returns:
            JSON事件列表
        """
        if not self.is_connected or not self.consumer:
            logger.warning("Kafka消费者未连接")
            return []

        events = []
        try:
            # 设置消费超时
            end_time = asyncio.get_event_loop().time() + timeout_seconds
            
            while len(events) < max_records and asyncio.get_event_loop().time() < end_time:
                try:
                    # 获取消息，设置较短的超时时间以便检查总超时
                    msg_map = await asyncio.wait_for(
                        self.consumer.getmany(timeout_ms=1000, max_records=max_records - len(events)),
                        timeout=1.0
                    )
                    
                    # 处理接收到的消息
                    for topic_partition, messages in msg_map.items():
                        for message in messages:
                            if message.value:
                                try:
                                    event = json.loads(message.value)
                                    events.append(event)
                                    logger.debug(f"接收到Kafka事件: {event}")
                                except json.JSONDecodeError as e:
                                    logger.warning(f"JSON解析失败: {e}, 原始数据: {message.value}")
                                    events.append({
                                        "error": f"JSON解析失败: {str(e)}",
                                        "raw_content": message.value
                                    })
                    
                    # 如果没有更多消息，稍等一下再试
                    if not msg_map:
                        await asyncio.sleep(0.1)
                        
                except asyncio.TimeoutError:
                    # 单次获取超时，继续尝试直到总超时
                    continue
                    
        except Exception as e:
            logger.error(f"消费Kafka事件失败: {e}")
            
        logger.info(f"从Kafka消费了 {len(events)} 个事件")
        return events

    async def get_recent_events(self, timeout_seconds: int = 10) -> List[Dict[str, Any]]:
        """获取最近的事件数据
        
        Args:
            timeout_seconds: 获取超时时间（秒）
            
        Returns:
            JSON事件列表
        """
        # 如果未连接，尝试连接
        if not self.is_connected:
            if not await self.connect():
                return []
        
        try:
            events = await self.consume_events(
                timeout_seconds=timeout_seconds,
                max_records=settings.kafka_max_poll_records
            )
            return events
            
        except Exception as e:
            logger.error(f"获取Kafka事件失败: {e}")
            return []

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()


# 全局Kafka客户端实例
kafka_client = KafkaClient()


async def get_kafka_events(timeout_seconds: int = 10) -> List[Dict[str, Any]]:
    """获取Kafka事件的便捷函数

    Args:
        timeout_seconds: 获取超时时间（秒）

    Returns:
        JSON事件列表
    """
    return await kafka_client.get_recent_events(timeout_seconds)


# 注册应用程序退出时的清理函数
import atexit
import asyncio

def cleanup_kafka_resources():
    """应用程序退出时清理Kafka资源"""
    if kafka_client.is_connected:
        logger.info("应用程序退出，正在清理Kafka资源...")
        try:
            # 创建新的事件循环来运行异步清理函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(kafka_client.disconnect())
            loop.close()
            logger.info("Kafka资源清理完成")
        except Exception as e:
            logger.error(f"Kafka资源清理失败: {e}")

# 注册退出清理函数
atexit.register(cleanup_kafka_resources)
